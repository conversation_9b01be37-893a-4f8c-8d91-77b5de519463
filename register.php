<?php include 'includes/header.php'; ?>

<div class="form-container">
  <h2>👤 Patient Registration</h2>
  <p style="text-align: center; margin-bottom: 2rem; color: #666;">
    Join <PERSON> and start your journey to better healthcare
  </p>

  <form method="post" action="">
    <div style="position: relative;">
      <input name="name" placeholder="Full Name" required style="padding-left: 40px;">
      <span style="position: absolute; left: 12px; top: 12px; color: #666;">👤</span>
    </div>

    <div style="position: relative;">
      <input name="dob" type="date" required style="padding-left: 40px;">
      <span style="position: absolute; left: 12px; top: 12px; color: #666;">🎂</span>
      <small style="color: #666; margin-top: -10px; display: block;">Date of Birth</small>
    </div>

    <div style="position: relative;">
      <input name="email" type="email" placeholder="Email Address" required style="padding-left: 40px;">
      <span style="position: absolute; left: 12px; top: 12px; color: #666;">📧</span>
    </div>

    <div style="position: relative;">
      <input name="phone" placeholder="Phone Number" required style="padding-left: 40px;">
      <span style="position: absolute; left: 12px; top: 12px; color: #666;">📱</span>
    </div>

    <div style="position: relative;">
      <input name="password" type="password" placeholder="Create Password" required style="padding-left: 40px;">
      <span style="position: absolute; left: 12px; top: 12px; color: #666;">🔒</span>
      <small style="color: #666; margin-top: -10px; display: block;">Choose a strong password</small>
    </div>

    <button type="submit" class="btn-primary">🚀 Create Account</button>
  </form>

  <p style="text-align: center; margin-top: 2rem;">
    Already have an account? <a href="login.php" style="color: #2c5aa0; text-decoration: none;">Login here</a>
  </p>
</div>
<?php
if ($_SERVER['REQUEST_METHOD']==='POST') {
  $stmt = $conn->prepare(
    "INSERT INTO patients (full_name,dob,email,phone)
     VALUES (?,?,?,?)"
  );

  if ($stmt === false) {
    echo "<p class='error'>Database error: " . $conn->error . "</p>";
    echo "<p class='error'>Please make sure the database tables are created. Run the database_setup.sql file.</p>";
  } else {
    $stmt->bind_param('ssss',
      $_POST['name'], $_POST['dob'],
      $_POST['email'], $_POST['phone']
    );
    if ($stmt->execute()) {
      $pid = $stmt->insert_id;
      $ph = password_hash($_POST['password'], PASSWORD_BCRYPT);
      $p2 = $conn->prepare(
        "INSERT INTO patient_logins (patient_id,password_hash)
         VALUES (?,?)"
      );
      if ($p2 === false) {
        echo "<p class='error'>Database error: " . $conn->error . "</p>";
      } else {
        $p2->bind_param('is', $pid, $ph);
        if ($p2->execute()) {
          header("Location: login.php?reg=1");
          exit;
        } else {
          echo "<p class='error'>Error creating login: " . $p2->error . "</p>";
        }
      }
    } else {
      echo "<p class='error'>Error: ".$stmt->error."</p>";
    }
  }
}
?>
<?php include 'includes/footer.php'; ?>
s