<?php include 'includes/header.php'; ?>
<h2>Patient Registration</h2>
<form method="post" action="">
  <input name="name" placeholder="Full Name" required>
  <input name="dob" type="date" required>
  <input name="email" type="email" required>
  <input name="phone" placeholder="Phone" required>
  <input name="password" type="password" required>
  <button type="submit">Register</button>
</form>
<?php
if ($_SERVER['REQUEST_METHOD']==='POST') {
  $stmt = $conn->prepare(
    "INSERT INTO patients (full_name,dob,email,phone)
     VALUES (?,?,?,?)"
  );

  if ($stmt === false) {
    echo "<p class='error'>Database error: " . $conn->error . "</p>";
    echo "<p class='error'>Please make sure the database tables are created. Run the database_setup.sql file.</p>";
  } else {
    $stmt->bind_param('ssss',
      $_POST['name'], $_POST['dob'],
      $_POST['email'], $_POST['phone']
    );
    if ($stmt->execute()) {
      $pid = $stmt->insert_id;
      $ph = password_hash($_POST['password'], PASSWORD_BCRYPT);
      $p2 = $conn->prepare(
        "INSERT INTO patient_logins (patient_id,password_hash)
         VALUES (?,?)"
      );
      if ($p2 === false) {
        echo "<p class='error'>Database error: " . $conn->error . "</p>";
      } else {
        $p2->bind_param('is', $pid, $ph);
        if ($p2->execute()) {
          header("Location: login.php?reg=1");
          exit;
        } else {
          echo "<p class='error'>Error creating login: " . $p2->error . "</p>";
        }
      }
    } else {
      echo "<p class='error'>Error: ".$stmt->error."</p>";
    }
  }
}
?>
<?php include 'includes/footer.php'; ?>
s