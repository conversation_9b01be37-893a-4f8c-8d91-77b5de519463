<?php include 'includes/header.php'; ?>
<h2>Patient Registration</h2>
<form method="post" action="">
  <input name="name" placeholder="Full Name" required>
  <input name="dob" type="date" required>
  <input name="email" type="email" required>
  <input name="phone" placeholder="Phone" required>
  <input name="password" type="password" required>
  <button type="submit">Register</button>
</form>
<?php
if ($_SERVER['REQUEST_METHOD']==='POST') {
  $stmt = $conn->prepare(
    "INSERT INTO Patients (Name,DOB,Email,Phone,LoyaltyStatus)
     VALUES (?,?,?,?, 'New')"
  );
  $stmt->bind_param('ssss',
    $_POST['name'], $_POST['dob'],
    $_POST['email'], $_POST['phone']
  );
  if ($stmt->execute()) {
    $pid = $stmt->insert_id;
    $ph = password_hash($_POST['password'], PASSWORD_BCRYPT);
    $p2 = $conn->prepare(
      "INSERT INTO PatientLogins (PatientID,PasswordHash)
       VALUES (?,?)"
    );
    $p2->bind_param('is', $pid, $ph);
    $p2->execute();
    header("Location: login.php?reg=1");
    exit;
  } else {
    echo "<p class='error'>Error: ".$conn->error."</p>";
  }
}
?>
<?php include 'includes/footer.php'; ?>
s