<?php
include 'includes/header.php';
if ($_SESSION['user_type']!=='patient') {
  header("Location: login.php"); exit;
}
$id = $_SESSION['user_id'];
$sql = "
  SELECT a.AppointmentDate,a.AppointmentTime,d.Name AS Doctor,a.Diagnosis
  FROM Appointments a
  JOIN Doctors d ON a.DoctorID=d.DoctorID
  WHERE a.PatientID=?
  ORDER BY a.AppointmentDate DESC
";
$st = $conn->prepare($sql);
$st->bind_param('i',$id);
$st->execute();
$res = $st->get_result();
?>
<h2>My Appointments</h2>
<table>
  <tr><th>Date</th><th>Time</th><th>Doctor</th><th>Diagnosis</th></tr>
  <?php while($r=$res->fetch_assoc()): ?>
    <tr>
      <td><?= $r['AppointmentDate'] ?></td>
      <td><?= $r['AppointmentTime'] ?></td>
      <td><?= $r['Doctor'] ?></td>
      <td><?= $r['Diagnosis'] ?></td>
    </tr>
  <?php endwhile; ?>
</table>
<?php include 'includes/footer.php'; ?>
