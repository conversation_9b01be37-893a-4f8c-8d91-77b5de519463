<?php
include 'includes/header.php';
if ($_SESSION['user_type']!=='patient') {
  header("Location: login.php"); exit;
}
$id = $_SESSION['user_id'];
$sql = "
  SELECT a.appointment_date,d.full_name AS Doctor,a.diagnosis
  FROM appointments a
  JOIN doctors d ON a.doctor_id=d.doctor_id
  WHERE a.patient_id=?
  ORDER BY a.appointment_date DESC
";
$st = $conn->prepare($sql);
$st->bind_param('i',$id);
$st->execute();
$res = $st->get_result();
?>
<h2>My Appointments</h2>
<table>
  <tr><th>Date & Time</th><th>Doctor</th><th>Diagnosis</th></tr>
  <?php while($r=$res->fetch_assoc()): ?>
    <tr>
      <td><?= $r['appointment_date'] ?></td>
      <td><?= $r['Doctor'] ?></td>
      <td><?= $r['diagnosis'] ?></td>
    </tr>
  <?php endwhile; ?>
</table>
<?php include 'includes/footer.php'; ?>
