<?php include 'includes/header.php'; ?>
<h2>Login</h2>
<form method="post" action="">
  <input name="email" type="email" required>
  <input name="password" type="password" required>
  <button type="submit">Log In</button>
</form>
<?php
if ($_SERVER['REQUEST_METHOD']==='POST') {
  // Patient login
  $s = $conn->prepare(
    "SELECT p.PatientID, pl.PasswordHash
     FROM Patients p
     JOIN PatientLogins pl ON p.PatientID=pl.PatientID
     WHERE p.Email=?"
  );
  $s->bind_param('s', $_POST['email']);
  $s->execute();
  $s->bind_result($uid,$hash);
  if ($s->fetch() && password_verify($_POST['password'], $hash)) {
    $_SESSION = ['user_type'=>'patient','user_id'=>$uid];
    header("Location: dashboard.php"); exit;
  }
  $s->close();
  echo "<p class='error'>Invalid credentials.</p>";
}
?>
<?php include 'includes/footer.php'; ?>
