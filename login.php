<?php include 'includes/header.php'; ?>
<h2>Login</h2>
<form method="post" action="">
  <input name="email" type="email" required>
  <input name="password" type="password" required>
  <button type="submit">Log In</button>
</form>
<?php
if ($_SERVER['REQUEST_METHOD']==='POST') {
  // Patient login
  $s = $conn->prepare(
    "SELECT p.patient_id, pl.password_hash
     FROM patients p
     JOIN patient_logins pl ON p.patient_id=pl.patient_id
     WHERE p.email=?"
  );

  if ($s === false) {
    echo "<p class='error'>Database error: " . $conn->error . "</p>";
    echo "<p class='error'>Please make sure the patient_logins table exists. Run the add_missing_tables.sql file.</p>";
  } else {
    $s->bind_param('s', $_POST['email']);
    $s->execute();
    $s->bind_result($uid,$hash);
    if ($s->fetch() && password_verify($_POST['password'], $hash)) {
      $_SESSION = ['user_type'=>'patient','user_id'=>$uid];
      header("Location: dashboard.php"); exit;
    }
    $s->close();
    echo "<p class='error'>Invalid credentials.</p>";
  }
}
?>
<?php include 'includes/footer.php'; ?>
