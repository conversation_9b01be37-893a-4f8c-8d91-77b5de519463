<?php
include 'includes/header.php';
if (!isset($_SESSION['user_id'])) {
  header("Location: login.php");
  exit;
}

// Get patient info
$patient_query = $conn->prepare("SELECT full_name, email, loyalty_status FROM patients WHERE patient_id = ?");
$patient_query->bind_param('i', $_SESSION['user_id']);
$patient_query->execute();
$patient = $patient_query->get_result()->fetch_assoc();

// Get recent appointments count
$apt_count_query = $conn->prepare("SELECT COUNT(*) as count FROM appointments WHERE patient_id = ?");
$apt_count_query->bind_param('i', $_SESSION['user_id']);
$apt_count_query->execute();
$apt_count = $apt_count_query->get_result()->fetch_assoc()['count'];

// Get recent orders count
$order_count_query = $conn->prepare("SELECT COUNT(*) as count FROM orders WHERE customer_id = ?");
$order_count_query->bind_param('i', $_SESSION['user_id']);
$order_count_query->execute();
$order_count = $order_count_query->get_result()->fetch_assoc()['count'];
?>

<div class="dashboard">
  <h2>Welcome back, <?= htmlspecialchars($patient['full_name']) ?>! 👋</h2>

  <?php if ($patient['loyalty_status'] == 'Loyal'): ?>
    <div class="success" style="text-align: center; margin: 2rem 0;">
      🎉 <strong>Congratulations!</strong> You are a Loyal Customer! Enjoy special benefits and discounts.
    </div>
  <?php endif; ?>

  <div style="background: #f8f9fa; padding: 1.5rem; border-radius: 10px; margin: 2rem 0; text-align: center;">
    <h3>Your Health Summary</h3>
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 1rem; margin-top: 1rem;">
      <div style="background: white; padding: 1rem; border-radius: 8px;">
        <div style="font-size: 2rem; color: #2c5aa0;">📅</div>
        <strong><?= $apt_count ?></strong><br>
        <small>Total Appointments</small>
      </div>
      <div style="background: white; padding: 1rem; border-radius: 8px;">
        <div style="font-size: 2rem; color: #2c5aa0;">🛒</div>
        <strong><?= $order_count ?></strong><br>
        <small>Total Orders</small>
      </div>
      <div style="background: white; padding: 1rem; border-radius: 8px;">
        <div style="font-size: 2rem; color: #2c5aa0;">⭐</div>
        <strong><?= ucfirst($patient['loyalty_status']) ?></strong><br>
        <small>Status</small>
      </div>
    </div>
  </div>

  <div class="dashboard-grid">
    <a href="book_appointment.php" class="dashboard-card">
      <h3>📅 Book Appointment</h3>
      <p>Schedule a consultation with our qualified doctors</p>
    </a>

    <a href="view_appointments.php" class="dashboard-card">
      <h3>📋 My Appointments</h3>
      <p>View your upcoming and past appointments</p>
    </a>

    <a href="order_product.php" class="dashboard-card">
      <h3>🛒 Order Products</h3>
      <p>Browse and order health products and medicines</p>
    </a>

    <a href="view_orders.php" class="dashboard-card">
      <h3>📦 My Orders</h3>
      <p>Track your product orders and delivery status</p>
    </a>
  </div>
</div>

<?php include 'includes/footer.php'; ?>
