<?php
include 'includes/header.php';
if ($_SESSION['user_type']!=='patient') {
  header("Location: login.php"); exit;
}
$prods = $conn->query("SELECT product_id,product_name,price,stock FROM products");
?>
<h2>Order Products</h2>
<form method="post" action="">
  <select name="product_id" required>
    <?php while($p=$prods->fetch_assoc()): ?>
      <option value="<?= $p['product_id'] ?>">
        <?= htmlspecialchars($p['product_name']) ?> (₹<?= $p['price'] ?>)
      </option>
    <?php endwhile; ?>
  </select>
  <input name="quantity" type="number" min="1" required>
  <select name="delivery_method">
    <option>Pickup</option>
    <option>Delivery</option>
  </select>
  <button type="submit">Order</button>
</form>
<?php
if ($_SERVER['REQUEST_METHOD']==='POST') {
  // Get product price
  $price_query = $conn->prepare("SELECT price FROM products WHERE product_id = ?");
  $price_query->bind_param('i', $_POST['product_id']);
  $price_query->execute();
  $price_result = $price_query->get_result();
  $product = $price_result->fetch_assoc();

  if ($product) {
    $total = $product['price'] * $_POST['quantity'];

    // Create order
    $st = $conn->prepare(
      "INSERT INTO orders (customer_id, total, order_status)
       VALUES (?, ?, 'Pending')"
    );
    $st->bind_param('id', $_SESSION['user_id'], $total);

    if ($st->execute()) {
      $order_id = $st->insert_id;

      // Add order item
      $item_st = $conn->prepare(
        "INSERT INTO order_items (order_id, product_id, quantity, price)
         VALUES (?, ?, ?, ?)"
      );
      $item_st->bind_param('iiid', $order_id, $_POST['product_id'], $_POST['quantity'], $product['price']);

      if ($item_st->execute()) {
        echo "<p class='success'>Order placed!</p>";

        // Update loyalty status
        $order_count_query = $conn->prepare("SELECT COUNT(*) as count FROM orders WHERE customer_id = ?");
        $order_count_query->bind_param('i', $_SESSION['user_id']);
        $order_count_query->execute();
        $count_result = $order_count_query->get_result();
        $order_count = $count_result->fetch_assoc()['count'];

        if ($order_count >= 5) {
          $u = $conn->prepare("UPDATE patients SET loyalty_status='Loyal' WHERE patient_id=?");
          $u->bind_param('i', $_SESSION['user_id']);
          $u->execute();
        }
      } else {
        echo "<p class='error'>Error adding order item: " . $item_st->error . "</p>";
      }
    } else {
      echo "<p class='error'>Error creating order: " . $st->error . "</p>";
    }
  } else {
    echo "<p class='error'>Product not found!</p>";
  }
}
?>
<?php include 'includes/footer.php'; ?>
