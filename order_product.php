<?php
include 'includes/header.php';
if ($_SESSION['user_type']!=='patient') {
  header("Location: login.php"); exit;
}
$prods = $conn->query("SELECT ProductID,Name,Price,Stock FROM Products");
?>
<h2>Order Products</h2>
<form method="post" action="">
  <select name="product_id" required>
    <?php while($p=$prods->fetch_assoc()): ?>
      <option value="<?= $p['ProductID'] ?>">
        <?= htmlspecialchars($p['Name']) ?> (₹<?= $p['Price'] ?>)
      </option>
    <?php endwhile; ?>
  </select>
  <input name="quantity" type="number" min="1" required>
  <select name="delivery_method">
    <option>Pickup</option>
    <option>Delivery</option>
  </select>
  <button type="submit">Order</button>
</form>
<?php
if ($_SERVER['REQUEST_METHOD']==='POST') {
  $st = $conn->prepare(
    "INSERT INTO Orders
     (CustomerID,ProductID,Quantity,OrderDate,DeliveryMethod)
     VALUES (?,?,?,?,?)"
  );
  $now = date('Y-m-d');
  $st->bind_param('iiiss',
    $_SESSION['user_id'],
    $_POST['product_id'],
    $_POST['quantity'],
    $now,
    $_POST['delivery_method']
  );
  if ($st->execute()) {
    echo "<p class='success'>Order placed!</p>";
    // update loyalty
    $u = $conn->prepare(
      "UPDATE Patients
       SET LoyaltyStatus='Loyal'
       WHERE PatientID=? AND
         (SELECT COUNT(*) FROM Orders WHERE CustomerID=?)>=5"
    );
    $u->bind_param('ii', $_SESSION['user_id'], $_SESSION['user_id']);
    $u->execute();
  }
}
?>
<?php include 'includes/footer.php'; ?>
