<?php
include 'includes/header.php';
if ($_SESSION['user_type']!=='patient') {
  header("Location: login.php"); exit;
}
$docs = $conn->query("SELECT doctor_id,full_name FROM doctors");
?>
<h2>Book Appointment</h2>
<form method="post" action="">
  <select name="doctor_id" required>
    <?php while($d=$docs->fetch_assoc()): ?>
      <option value="<?= $d['doctor_id'] ?>"><?= $d['full_name'] ?></option>
    <?php endwhile; ?>
  </select>
  <input name="date" type="date" required>
  <input name="time" type="time" required>
  <input name="consultation_type" placeholder="Type" required>
  <button type="submit">Book</button>
</form>
<?php
if ($_SERVER['REQUEST_METHOD']==='POST') {
  $appointment_datetime = $_POST['date'] . ' ' . $_POST['time'];
  $st = $conn->prepare(
    "INSERT INTO appointments
     (patient_id,doctor_id,appointment_date,consultation_type)
     VALUES (?,?,?,?)"
  );
  $st->bind_param('iiss',
    $_SESSION['user_id'],
    $_POST['doctor_id'],
    $appointment_datetime,
    $_POST['consultation_type']
  );
  if ($st->execute()) {
    echo "<p class='success'>Appointment booked!</p>";
  }
}
?>
<?php include 'includes/footer.php'; ?>
