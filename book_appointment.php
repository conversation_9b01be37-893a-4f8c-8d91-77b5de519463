<?php
include 'includes/header.php';
if ($_SESSION['user_type']!=='patient') {
  header("Location: login.php"); exit;
}
$docs = $conn->query("SELECT DoctorID,Name FROM Doctors");
?>
<h2>Book Appointment</h2>
<form method="post" action="">
  <select name="doctor_id" required>
    <?php while($d=$docs->fetch_assoc()): ?>
      <option value="<?= $d['DoctorID'] ?>"><?= $d['Name'] ?></option>
    <?php endwhile; ?>
  </select>
  <input name="date" type="date" required>
  <input name="time" type="time" required>
  <input name="consultation_type" placeholder="Type" required>
  <button type="submit">Book</button>
</form>
<?php
if ($_SERVER['REQUEST_METHOD']==='POST') {
  $st = $conn->prepare(
    "INSERT INTO Appointments
     (Pat<PERSON><PERSON>,DoctorID,AppointmentDate,AppointmentTime,ConsultationType)
     VALUES (?,?,?,?,?)"
  );
  $st->bind_param('iisss',
    $_SESSION['user_id'],
    $_POST['doctor_id'],
    $_POST['date'],
    $_POST['time'],
    $_POST['consultation_type']
  );
  if ($st->execute()) {
    echo "<p class='success'>Appointment booked!</p>";
  }
}
?>
<?php include 'includes/footer.php'; ?>
